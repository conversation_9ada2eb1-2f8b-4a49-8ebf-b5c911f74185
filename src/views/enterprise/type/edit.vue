<template>
  <EditPageContainer
      :title="pageTitle"
      :icon="pageIcon"
      :breadcrumb-items="breadcrumbItems"
    :is-view="isView"
    :loading="loading"
      @back="handleBack"
    @save="handleSave"
      @breadcrumb-click="handleBreadcrumbClick"
    >
    <UniversalForm
      ref="universalForm"
      :formData="form"
      :formRules="formRules"
      :formGroups="formGroups"
      :labelWidth="'100px'"
      :rowGutter="20"
      :isView="isView"
      hide-required-asterisk
    />
  </EditPageContainer>
</template>

<script>
import EditPageContainer from '@/components/layouts/EditPageContainer.vue'
import UniversalForm from '@/components/layouts/UniversalForm.vue'
import { getEnterpriseTypeDetail, createEnterpriseType, updateEnterpriseType } from '@/api/enterprise/type.js'

export default {
  name: 'EnterpriseTypeEdit',
  components: { EditPageContainer, UniversalForm },
  data() {
    return {
      isEdit: false,
      isView: false,
      loading: false,
      rulesLoading: false,
      form: {
        name: '',
        code: '',
        description: '',
        priority: 1,
        rules: []
      },
      formGroups: [
        {
          title: '基本信息',
          icon: 'el-icon-info',
          fields: [
            [
              { prop: 'name', label: '类型名称', type: 'input', placeholder: '请输入企业类型名称', maxlength: 50, showWordLimit: true },
              { prop: 'code', label: '类型编码', type: 'input', placeholder: '请输入类型编码，如：SMALL', maxlength: 20 },
              { prop: 'priority', label: '优先级', type: 'number', placeholder: '请设置优先级', min: 1, max: 10, step: 1 }
            ],
            [
              { prop: 'description', label: '类型描述', type: 'textarea', placeholder: '请输入类型描述', maxlength: 200, rows: 3, showWordLimit: true, span: 24 }
            ]
          ]
        },
        {
          title: '判定规则',
          icon: 'el-icon-s-operation',
          fields: [
            [
              {
                type: 'list',
                prop: 'rules',
                label: '判定规则',
                icon: 'el-icon-s-operation',
                min: 1,
                defaultRow: () => ({ field: 'employeeCount', operator: '<=', value: '' }),
                columns: [
                  {
                    prop: 'field',
                    label: '字段',
                    type: 'select',
                    width: 150,
                    options: [
                      { label: '人员规模', value: 'employeeCount' },
                      { label: '营收规模', value: 'revenue' }
                    ],
                    onChange: (value, row, index, formData) => {
                      row.value = ''
                    }
                  },
                  {
                    prop: 'operator',
                    label: '操作符',
                    type: 'select',
                    width: 120,
                    options: [
                      { label: '大于', value: '>' },
                      { label: '大于等于', value: '>=' },
                      { label: '小于', value: '<' },
                      { label: '小于等于', value: '<=' },
                      { label: '等于', value: '=' }
                    ]
                  },
                  {
                    prop: 'value',
                    label: '数值',
                    type: 'number',
                    minWidth: 150,
                    placeholder: '请输入数值',
                    formatter: (val, row) => {
                      if (!val) return '-'
                      const units = { employeeCount: '人', revenue: '元' }
                      return `${val}${units[row.field] || ''}`
                    }
                  }
                ],
              }
            ]
          ]
        }
      ],
      formRules: {
        name: [{ required: true, message: '请输入类型名称', trigger: 'blur' }],
        code: [{ required: true, message: '请输入类型编码', trigger: 'blur' }],
        priority: [{ required: true, message: '请设置优先级', trigger: 'blur' }]
      }
    }
  },
  computed: {
    pageTitle() {
      if (this.isView) return '查看企业类型'
      return this.isEdit ? '编辑企业类型' : '新建企业类型'
    },
    pageIcon() {
      return 'el-icon-s-data'
    },
    breadcrumbItems() {
      return [
        { text: '企业类型管理', to: { name: 'enterpriseType' }, icon: 'el-icon-s-data' },
        { text: this.pageTitle, icon: this.pageIcon }
      ]
    }
  },
  created() {
    const id = this.$route.params.id
    const mode = this.$route.query.mode
    if (mode === 'view') this.isView = true
    if (id) {
      this.isEdit = true
      this.loadData(id)
    }
  },
  methods: {
    async loadData(id) {
      try {
        const response = await getEnterpriseTypeDetail(id)
        // 后端直接返回数据，不需要判断code
        this.form = { ...response }
      } catch (error) {
        this.$message.error('加载数据失败')
        console.error('加载数据失败:', error)
      }
    },
    async handleSave() {
      await this.$refs.universalForm.validate()
          if (this.form.rules.length === 0) {
            this.$message.warning('请至少添加一条判定规则')
            return
          }
          this.loading = true
          try {
            if (this.isEdit) {
              // 更新时需要传入id
              const updateData = { ...this.form, id: this.$route.params.id }
              await updateEnterpriseType(updateData)
            } else {
              await createEnterpriseType(this.form)
            }
            this.$message.success(this.isEdit ? '更新成功' : '创建成功')
            this.$router.back()
          } catch (error) {
            this.$message.error('保存失败')
            console.error('保存失败:', error)
          } finally {
            this.loading = false
          }
    },
    handleBack() {
      this.$router.back()
    },
    handleBreadcrumbClick(item) {
      if (item.to && item.to.name) {
        this.$router.push({ name: item.to.name })
      }
    }
  }
}
</script>
